<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "0CBA09D9-5831-4F46-AFF0-44A153AD256B"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SessionManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "60"
            endingLineNumber = "60"
            landmarkName = "openSession(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 904eefcaf94ace19"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 904eefcaf94ace19"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 426e7d74f34be616"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (PhotoUncle.Session) -&gt; Swift.Bool in PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8460A60A-269F-439F-9227-63C953D55404"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "184"
            endingLineNumber = "184"
            landmarkName = "init(url:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
