//
//  OptimizedThumbListView.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI

struct OptimizedThumbListView: View {
    @ObservedObject var viewModel: ThumbListViewModel

    var body: some View {
        Group {
            if !viewModel.photos.isEmpty {
                optimizedThumbnailView
            } else {
                EmptyView()
            }
        }

    }
    
    // MARK: - Optimized Thumbnail View

    @ViewBuilder
    private var optimizedThumbnailView: some View {
        ScrollView(isHorizontal ? .horizontal : .vertical, showsIndicators: true) {
            if isHorizontal {
                LazyHGrid(rows: [GridItem(.fixed(80), spacing: 8)], spacing: 8) {
                    thumbnailCells
                }
                .padding(4)
            } else {
                LazyVGrid(columns: [GridItem(.fixed(80), spacing: 8)], spacing: 8) {
                    thumbnailCells
                }
                .padding(4)
            }
        }
        .frame(
            maxWidth: isHorizontal ? .infinity : 120,
            maxHeight: isHorizontal ? 120 : .infinity
        )
        .background(Color.gray.opacity(0.1))
    }

    @ViewBuilder
    private var thumbnailCells: some View {
        ForEach(viewModel.photos, id: \.id) { photo in
            ThumbnailCell(
                photo: photo,
                isSelected: photo == viewModel.selectedPhoto,
                onTap: {
                    viewModel.selectPhoto(photo)
                }
            )
            .id(photo.id)
        }
    }
    
    private var isHorizontal: Bool {
        viewModel.thumbnailPosition == .top || viewModel.thumbnailPosition == .bottom
    }
}

// MARK: - Thumbnail Cell

struct ThumbnailCell: View {
    let photo: PhotoItem
    let isSelected: Bool
    let onTap: () -> Void

    @State private var thumbnail: NSImage?
    @State private var isLoading = true
    @State private var loadTask: Task<Void, Never>?

    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.2))

            // 缩略图或加载指示器
            Group {
                if let thumbnail = thumbnail {
                    Image(nsImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipped()
                        .transition(.opacity.animation(.easeInOut(duration: 0.2)))
                } else if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle())
                } else {
                    Image(systemName: "photo")
                        .foregroundColor(.secondary)
                }
            }
        }
        .frame(width: 80, height: 80)
        .overlay(
            RoundedRectangle(cornerRadius: 4)
                .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                .animation(.easeInOut(duration: 0.15), value: isSelected)
        )
        .onTapGesture {
            onTap()
        }
        .onAppear {
            startLoadingThumbnail()
        }
        .onDisappear {
            cancelLoading()
        }
        .onChange(of: photo.id) { _ in
            startLoadingThumbnail()
        }
    }
    
    // MARK: - Thumbnail Loading

    private func startLoadingThumbnail() {
        // 取消之前的加载任务
        cancelLoading()

        isLoading = true
        thumbnail = nil

        loadTask = Task {
            // 使用异步方法生成缩略图
            let loadedThumbnail = await ThumbnailGenerator.shared.generateThumbnailAsync(for: photo.url)

            // 检查任务是否被取消
            guard !Task.isCancelled else { return }

            await MainActor.run {
                thumbnail = loadedThumbnail
                isLoading = false
            }
        }
    }

    private func cancelLoading() {
        loadTask?.cancel()
        loadTask = nil
    }
}

#Preview {
    // 创建预览数据
    let photoLoader = PhotoLoader()
    let thumbListViewModel = ThumbListViewModel(photoLoader: photoLoader)
    
    return OptimizedThumbListView(viewModel: thumbListViewModel)
        .frame(width: 400, height: 100)
}
