//
//  SettingsWindow.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI

struct SettingsWindow: View {
    var onClose: (() -> Void)? = nil
    @State private var dragSpeed: Double = UserDefaults.standard.double(forKey: "dragSpeed")
    @State private var resetHorizontalOffset: Bool = UserDefaults.standard.bool(forKey: "resetHorizontalOffset")
    @State private var resetVerticalOffset: Bool = UserDefaults.standard.bool(forKey: "resetVerticalOffset")
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("图片查看器设置")
                .font(.title2)
                .fontWeight(.bold)
                .padding(.top, 20)
            
            // 速度系数设置
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("拖动速度系数")
                        .font(.headline)
                    Spacer()
                    Text("\(String(format: "%.1f", dragSpeed))x")
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(.secondary)
                }
                
                Slider(value: $dragSpeed, in: 0.1...10.0, step: 0.1) {
                    Text("拖动速度系数")
                }
                .accentColor(.blue)
                
                Text("影响键盘上下箭头和滚轮拖动的速度")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            
            Divider()
                .padding(.horizontal, 20)
            
            // 重置行为设置
            VStack(alignment: .leading, spacing: 12) {
                Text("切换时重置行为")
                    .font(.headline)
                    .padding(.horizontal, 20)
                
                VStack(spacing: 8) {
                    // 水平偏移重置
                    HStack {
                        Toggle("重置水平偏移", isOn: $resetHorizontalOffset)
                            .toggleStyle(SwitchToggleStyle())
                        
                        Spacer()
                        
                        Text("上下移动时自动水平居中")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 垂直偏移重置
                    HStack {
                        Toggle("重置垂直偏移", isOn: $resetVerticalOffset)
                            .toggleStyle(SwitchToggleStyle())
                        
                        Spacer()
                        
                        Text("缩放时自动垂直居中")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 20)
            }

            Divider()
                .padding(.horizontal, 20)

            // 性能监控
            VStack(alignment: .leading, spacing: 8) {
                Text("性能监控")
                    .font(.headline)
                    .padding(.horizontal, 20)

                ThumbnailPerformanceView()
                    .padding(.horizontal, 20)
            }

            Spacer()

            // 底部按钮
            HStack {
                Spacer()
                Button("重置为默认") {
                    dragSpeed = 1.0
                    resetHorizontalOffset = true
                    resetVerticalOffset = true
                }
                .buttonStyle(.bordered)
                
                Button("确定") {
                    // 关闭当前窗口
                    UserDefaults.standard.set(dragSpeed, forKey: "dragSpeed")
                    UserDefaults.standard.set(resetHorizontalOffset, forKey: "resetHorizontalOffset")
                    UserDefaults.standard.set(resetVerticalOffset, forKey: "resetVerticalOffset")
                    onClose?()
//                    if let window = NSApplication.shared.windows.first(where: { $0.isKeyWindow }) {
//                        window.close()
//                    }
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(width: 450, height: 500)
        .background(Color(NSColor.controlBackgroundColor))
    }
}

#Preview {
    SettingsWindow()
} 
