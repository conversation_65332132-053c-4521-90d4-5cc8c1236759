//
//  SDImageViewer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import SDWebImageSwiftUI

struct SDImageViewer: View {
    let imageURL: URL

    @State private var scale: CGFloat = 1.0
    @State private var rotation: Angle = .degrees(0)
    @State private var offset: CGSize = .zero
    @State private var dragOffset: CGSize = .zero
    @State private var imageSize: CGSize = .zero
    @State private var showControls: Bool = true
    @State private var isLoading: Bool = true
    @State private var lastScale: CGFloat = 1.0
    @State private var hideControlsTimer: Timer?
    @State private var isDragging: Bool = false
    @AppStorage("dragSpeed") private var dragSpeed: Double = 1.0 // 拖动系数，范围0.1-10
    @AppStorage("resetHorizontalOffset") private var resetHorizontalOffset: Bool = true // 是否重置水平偏移
    @AppStorage("resetVerticalOffset") private var resetVerticalOffset: Bool = true // 是否重置垂直偏移
    private let scrollFactor = 5.0

    // 优化缩放范围
    private let minScale: CGFloat = 0.1
    private let maxScale: CGFloat = 50.0

    var body: some View {
        GeometryReader { geometry in
            let containerSize = geometry.size
            let fitScale = fitScaleFor(imageSize: imageSize, containerSize: containerSize)

            ZStack {
                // 渐变背景
                LinearGradient(
                    colors: [Color.black, Color.gray.opacity(0.3), Color.black],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )

                if isLoading {
                    // 加载指示器
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        Text("加载中...")
                            .foregroundColor(.white.opacity(0.8))
                            .font(.system(size: 14, weight: .medium))
                    }
                }

                ZStack {
                    // 手势处理层 - 放在最底层，覆盖整个区域
                    GestureHandler(
                        onDragChanged: { translation in
                            print("🎯 拖拽中: \(translation)")
                            isDragging = true
                            dragOffset = translation
                        },
                        onDragEnded: { translation in
                            print("🎯 拖拽结束: \(translation)")
                            isDragging = false
                            offset.width += translation.width
                            offset.height += translation.height
                            dragOffset = .zero
                        },
                        onScrollWheel: { deltaY, isCmdPressed in
                            print("🎯 滚轮事件: deltaY = \(deltaY), Cmd = \(isCmdPressed)")
                            if isCmdPressed {
                                // Cmd + 滚轮 = 上下拖动
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    offset.height -= deltaY * scale * 2 * CGFloat(dragSpeed) // 应用拖动系数
                                    // 根据设置决定是否重置水平偏移
                                    if resetHorizontalOffset {
                                        offset.width = 0
                                    }
                                }
                            } else {
                                // 普通滚轮 = 缩放
                                let zoomFactor: CGFloat = 1.1
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    if deltaY > 0 {
                                        scale = min(maxScale, scale * zoomFactor)
                                    } else {
                                        scale = max(minScale, scale / zoomFactor)
                                    }
                                    // 根据设置决定是否重置偏移
                                    if resetHorizontalOffset {
                                        offset.width = 0
                                    }
                                    if resetVerticalOffset {
                                        offset.height = 0
                                    }
                                }
                            }
                        }
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)

                    // 图片显示层
                    WebImage(url: imageURL)
                        .onSuccess { image, data, cacheType in
                            // 获取图片尺寸
                            print("🖼️ 图片加载成功: \(image.size)")
                            imageSize = image.size
                            isLoading = false
                        }
                        .onFailure { error in
                            print("❌ 图片加载失败: \(error)")
                            isLoading = false
                        }
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(fitScale * scale)
                        .rotationEffect(rotation)
                        .offset(x: offset.width + dragOffset.width,
                                y: offset.height + dragOffset.height)
                        .overlay(
                            // 拖动时的视觉反馈
                            Group {
                                if isDragging {
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 2)
                                        .scaleEffect(1.02)
                                        .animation(.easeInOut(duration: 0.1), value: isDragging)
                                }
                            }
                        )
                        .allowsHitTesting(false) // 禁用图片的点击事件，让手势处理层处理
                }
                    .onAppear {
                        print("🔄 SDImageViewer appeared")
                    }
                    .onDisappear {
                        print("🔄 SDImageViewer disappeared")
                    }
                    .simultaneousGesture(
                        // 缩放手势 - 使用simultaneousGesture与拖拽共存
                        MagnificationGesture()
                            .onChanged { value in
                                print("🔍 缩放手势: value = \(value), lastScale = \(lastScale)")
                                let delta = value / lastScale
                                scale = min(maxScale, max(minScale, scale * delta))
                                lastScale = value
                                print("🔍 新scale = \(scale)")
                            }
                            .onEnded { _ in
                                print("🔍 缩放手势结束")
                                lastScale = 1.0
                            }
                    )
                    .onTapGesture(count: 1) {
                        // 单击显示/隐藏控制栏
                        print("👆 单击手势")
                        if !isDragging {
                            print("👆 切换控制栏显示")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls.toggle()
                            }
                        }
                    }
                    .onKeyPress(.upArrow) {
                        // 键盘上箭头 = 向上拖动
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            offset.height -= 50 * CGFloat(dragSpeed)
                            // 根据设置决定是否重置水平偏移
                            if resetHorizontalOffset {
                                offset.width = 0
                            }
                        }
                        return .handled
                    }
                    .onKeyPress(.downArrow) {
                        // 键盘下箭头 = 向下拖动
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            offset.height += 50 * CGFloat(dragSpeed)
                            // 根据设置决定是否重置水平偏移
                            if resetHorizontalOffset {
                                offset.width = 0
                            }
                        }
                        return .handled
                    }
                    .onHover { hovering in
                        print("🖱️ hover: \(hovering)")
                        // 取消之前的隐藏定时器
                        hideControlsTimer?.invalidate()
                        
                        if hovering {
                            // 鼠标进入时立即显示控制栏
                            print("🖱️ 显示控制栏")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls = true
                            }
                        } else {
                            // 鼠标离开时延迟隐藏控制栏，避免在控制面板上操作时闪烁
                            print("🖱️ 延迟隐藏控制栏")
                            hideControlsTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showControls = false
                                }
                            }
                        }
                    }

                // 控制面板 - 只在显示时可见
                if showControls {
                    // 底部统一控制面板
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            unifiedControlPanel
                                .padding(.horizontal, 20)
                                .padding(.bottom, 20)
                                .onHover { hovering in
                                    // 当鼠标悬停在控制面板上时，取消隐藏定时器并保持显示状态
                                    hideControlsTimer?.invalidate()
                                    if hovering {
                                        showControls = true
                                    }
                                }
                        }
                    }
                }
            }
        }
        .clipped()
        .background(Color.black)
        .onDisappear {
            // 清理定时器
            hideControlsTimer?.invalidate()
            hideControlsTimer = nil
        }
    }



    var unifiedControlPanel: some View {
        ZStack {
            // 透明背景 - 不阻挡手势
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                .allowsHitTesting(false) // 背景不阻挡手势
            
            // 统一控制内容 - 可以接收点击
            HStack(spacing: 20) {
                // 左侧：旋转和重置按钮
                HStack(spacing: 12) {
                    // 旋转控制
                    ControlButton(
                        icon: "rotate.left",
                        action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                rotation += .degrees(-90)
                            }
                        }
                    )
                    
                    ControlButton(
                        icon: "rotate.right",
                        action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                rotation += .degrees(90)
                            }
                        }
                    )

                    Divider()
                        .frame(height: 24)
                        .background(Color.white.opacity(0.3))

                    // 重置按钮
                    ControlButton(
                        icon: "arrow.counterclockwise",
                        action: {
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                scale = 1.0
                                rotation = .zero
                                offset = .zero
                            }
                        }
                    )
                }

                Divider()
                    .frame(height: 40)
                    .background(Color.white.opacity(0.3))

                // 中间：缩放控制
                HStack(spacing: 12) {
                    ControlButton(
                        icon: "minus.magnifyingglass",
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                scale = max(minScale, scale / 1.2) // 缩小
                            }
                        }
                    )
                    
                    // 缩放百分比显示
                    Text("\(Int(scale * 100))%")
                        .foregroundColor(.white)
                        .font(.system(size: 14, weight: .semibold))
                        .monospacedDigit()
                        .frame(width: 60)
                    
                    ControlButton(
                        icon: "plus.magnifyingglass",
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                scale = min(maxScale, scale * 1.2) // 放大
                            }
                        }
                    )
                }

                Divider()
                    .frame(height: 40)
                    .background(Color.white.opacity(0.3))

                // 右侧：缩放滑块
                HStack(spacing: 12) {
                    Slider(value: $scale, in: minScale...maxScale) {
                        Text("缩放").foregroundColor(.white).font(.system(size: 13, weight: .medium))
                    }
                    .accentColor(.white)
                    .frame(width: 120)
                }
            }
            .padding(16)
        }
        .frame(height: 72) // 恢复原来的高度
        .frame(maxWidth: 600) // 限制最大宽度
    }



    /// 优化初始图片显示尺寸计算
    func fitScaleFor(imageSize: CGSize, containerSize: CGSize) -> CGFloat {
        guard imageSize.width > 0 && imageSize.height > 0 &&
              containerSize.width > 0 && containerSize.height > 0 else { return 1.0 }

        // 计算适配缩放比例
        let scaleToFitWidth = containerSize.width / imageSize.width
        let scaleToFitHeight = containerSize.height / imageSize.height

        // 选择较小的缩放比例以确保图片完全显示在容器内
        let fitScale = min(scaleToFitWidth, scaleToFitHeight)

        // 优化长图的显示
        let aspectRatio = imageSize.width / imageSize.height
        
        if aspectRatio < 0.3 {
            // 非常长的竖图，确保最小宽度为容器的50%
            let minWidth = containerSize.width * 0.5
            let widthScale = minWidth / imageSize.width
            return max(fitScale, widthScale)
        } else if aspectRatio < 0.7 {
            // 较长的竖图，确保最小宽度为容器的40%
            let minWidth = containerSize.width * 0.4
            let widthScale = minWidth / imageSize.width
            return max(fitScale, widthScale)
        } else if aspectRatio > 3.0 {
            // 非常宽的横图，确保最小高度为容器的50%
            let minHeight = containerSize.height * 0.5
            let heightScale = minHeight / imageSize.height
            return max(fitScale, heightScale)
        } else if aspectRatio > 1.5 {
            // 较宽的横图，确保最小高度为容器的40%
            let minHeight = containerSize.height * 0.4
            let heightScale = minHeight / imageSize.height
            return max(fitScale, heightScale)
        }

        // 对于正常比例的图片，允许适当放大
        if fitScale > 1.0 {
            return min(fitScale, 2.0) // 最大放大2倍
        }

        return fitScale
    }
}

// 手势处理器组件
struct GestureHandler: NSViewRepresentable {
    let onDragChanged: (CGSize) -> Void
    let onDragEnded: (CGSize) -> Void
    let onScrollWheel: (CGFloat, Bool) -> Void
    
    func makeNSView(context: Context) -> NSView {
        print("🔧 创建GestureHandler NSView")
        let view = GestureView()
        view.onDragChanged = onDragChanged
        view.onDragEnded = onDragEnded
        view.onScrollWheel = onScrollWheel
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新GestureHandler NSView (1)")
    }
    
    class GestureView: NSView {
        var onDragChanged: ((CGSize) -> Void)?
        var onDragEnded: ((CGSize) -> Void)?
        var onScrollWheel: ((CGFloat, Bool) -> Void)?
        private var initialDragLocation: NSPoint?
        private var totalTranslation: CGSize = .zero

        override init(frame frameRect: NSRect) {
            super.init(frame: frameRect)
            setupView()
        }

        required init?(coder: NSCoder) {
            super.init(coder: coder)
            setupView()
        }

        private func setupView() {
            // 确保视图可以接收鼠标事件
            wantsLayer = true
            layer?.backgroundColor = NSColor.clear.cgColor
        }

        override func mouseDown(with event: NSEvent) {
            print("🖱️ mouseDown: \(event.locationInWindow)")
            // 转换为视图坐标
            let locationInView = convert(event.locationInWindow, from: nil)
            initialDragLocation = locationInView
            totalTranslation = .zero
            window?.makeFirstResponder(self)
        }

        override func mouseDragged(with event: NSEvent) {
            print("🖱️ mouseDragged: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else {
                print("❌ 没有初始位置")
                return
            }
            // 转换为视图坐标
            let currentLocation = convert(event.locationInWindow, from: nil)

            // 计算偏移量，注意 macOS 坐标系转换
            totalTranslation = CGSize(
                width: currentLocation.x - initialLocation.x,
                height: -(currentLocation.y - initialLocation.y) // 翻转 Y 轴
            )
            print("📏 计算偏移: \(totalTranslation)")
            onDragChanged?(totalTranslation)
        }

        override func mouseUp(with event: NSEvent) {
            print("🖱️ mouseUp: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else {
                print("❌ 没有初始位置")
                return
            }
            // 转换为视图坐标
            let currentLocation = convert(event.locationInWindow, from: nil)

            // 计算最终偏移量，注意 macOS 坐标系转换
            let finalTranslation = CGSize(
                width: currentLocation.x - initialLocation.x,
                height: -(currentLocation.y - initialLocation.y) // 翻转 Y 轴
            )
            print("📏 最终偏移: \(finalTranslation)")
            onDragEnded?(finalTranslation)
            initialDragLocation = nil
            totalTranslation = .zero
        }

        override func scrollWheel(with event: NSEvent) {
            print("🖱️ scrollWheel: deltaY = \(event.scrollingDeltaY), deltaX = \(event.scrollingDeltaX)")
            // 处理滚轮事件，优先处理垂直滚动
            let deltaY = event.scrollingDeltaY
            let isCmdPressed = event.modifierFlags.contains(.command)
            
            if abs(deltaY) > 0.1 { // 添加阈值避免微小滚动
                onScrollWheel?(deltaY, isCmdPressed)
            }
        }

        override var acceptsFirstResponder: Bool {
            print("🔧 acceptsFirstResponder: true")
            return true
        }

        override func becomeFirstResponder() -> Bool {
            print("🔧 becomeFirstResponder: true")
            return true
        }

        override func updateTrackingAreas() {
            super.updateTrackingAreas()

            // 移除旧的跟踪区域
            for trackingArea in trackingAreas {
                removeTrackingArea(trackingArea)
            }

            // 添加新的跟踪区域以接收鼠标事件
            let trackingArea = NSTrackingArea(
                rect: bounds,
                options: [.activeInKeyWindow, .mouseEnteredAndExited, .mouseMoved],
                owner: self,
                userInfo: nil
            )
            addTrackingArea(trackingArea)
        }
    }
}

// 控制按钮组件
struct ControlButton: View {
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    Circle()
                        .fill(.ultraThinMaterial)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: true)
        .allowsHitTesting(true) // 确保按钮可以接收点击事件
    }
}



// 扩展View以支持滚轮事件（保留以备后用）
extension View {
    func onScrollWheel(perform action: @escaping (NSEvent) -> Void) -> some View {
        self.background(ScrollWheelHandler(onScrollWheel: action))
    }
}

struct ScrollWheelHandler: NSViewRepresentable {
    let onScrollWheel: (NSEvent) -> Void

    func makeNSView(context: Context) -> NSView {
        let view = ScrollWheelView()
        view.onScrollWheel = onScrollWheel
        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新ScrollWheelHandler NSView (2)")
    }

    class ScrollWheelView: NSView {
        var onScrollWheel: ((NSEvent) -> Void)?

        override func scrollWheel(with event: NSEvent) {
            onScrollWheel?(event)
        }

        override var acceptsFirstResponder: Bool {
            return true
        }
        
        override func becomeFirstResponder() -> Bool {
            return true
        }
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        SDImageViewer(imageURL: url)
            .frame(width: 800, height: 600)
    }
}
