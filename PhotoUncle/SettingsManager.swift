//
//  SettingsManager.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import AppKit

class SettingsManager: NSObject, ObservableObject {
    static let shared = SettingsManager()
    
    // 保持对设置窗口的强引用
    public weak var settingsWindow: NSWindow?
    
    override private init() {}
    
    func openSettings() {
        // 如果设置窗口已经存在，将其前置
        if let existingWindow = settingsWindow, existingWindow.isVisible {
            existingWindow.makeKeyAndOrderFront(nil)
            return
        }
        
        // 创建新的设置窗口
        let settingsView = SettingsWindow(onClose: { [weak self] in
            DispatchQueue.main.async {
                        self?.settingsWindow?.close()
                self?.settingsWindow = nil
                    }
                })
        let hostingController = NSHostingController(rootView: settingsView)
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 450, height: 500),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "PhotoUncle 设置"
        window.contentViewController = hostingController
        window.center()
        window.makeKeyAndOrderFront(nil)
        
        // 设置窗口关闭时的回调
//        window.delegate = self
        
        // 保持对窗口的强引用
        self.settingsWindow = window
    }
}

//// MARK: - NSWindowDelegate
//extension SettingsManager: NSWindowDelegate {
//    func windowWillClose(_ notification: Notification) {
//        // 窗口即将关闭时，清除引用
//        if let window = notification.object as? NSWindow, window === settingsWindow {
//            settingsWindow = nil
//        }
//    }
//}

