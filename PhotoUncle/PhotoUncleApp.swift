//
//  PhotoUncleApp.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI

@main
struct PhotoUncleApp: App {
    @StateObject private var sessionManager = SessionManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(sessionManager)
        }
        .commands {
            // 会话菜单
            CommandGroup(after: .newItem) {
                Menu("会话") {
                    // 最近会话
                    if !sessionManager.sessions.isEmpty {
                        Section("最近会话") {
                            ForEach(sessionManager.getRecentSessions(limit: 10)) { session in
                                Button(action: {
                                    openSession(session)
                                }) {
                                    VStack(alignment: .leading) {
                                        Text(session.name)
                                        Text(session.directoryPath)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .keyboardShortcut(getKeyboardShortcut(for: session))
                            }
                        }
                        
                        Divider()
                    }
                    
                    // 会话管理
                    Section("管理") {
                        Button("清除所有会话") {
                            sessionManager.clearAllSessions()
                        }
                        .disabled(sessionManager.sessions.isEmpty)
                    }
                }
            }
            
            // 设置菜单
            CommandGroup(after: .appInfo) {
                Button("偏好设置...") {
                    SettingsManager.shared.openSettings()
                }
                .keyboardShortcut(",", modifiers: .command)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func openSession(_ session: Session) {
        // 检查目录是否仍然有效
        guard session.isDirectoryValid else {
            print("❌ 目录不存在，从会话列表中移除: \(session.directoryPath)")
            sessionManager.removeSession(session)
            return
        }
        
        // 更新session访问时间
        sessionManager.openSession(session)
        
        // 通知ContentView打开会话
        NotificationCenter.default.post(
            name: NSNotification.Name("OpenSession"),
            object: session
        )
    }
    
    private func getKeyboardShortcut(for session: Session) -> KeyboardShortcut? {
        // 为前9个会话分配快捷键 Cmd+1 到 Cmd+9
        if let index = sessionManager.sessions.firstIndex(where: { $0.id == session.id }),
           index < 9 {
            let keyEquivalent = String(index + 1)
            return KeyboardShortcut(KeyEquivalent(Character(keyEquivalent)), modifiers: .command)
        }
        return nil
    }
}
